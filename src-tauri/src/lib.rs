
use std::process::Command;
use std::fs;
use std::env;
use std::time::Duration;
use std::sync::Mutex;
use serde::{Deserialize, Serialize};
use tauri::{Manager, Emitter};
use base64::Engine;
use tauri_plugin_shell::ShellExt;
use tokio::time::interval;

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessingResult {
    success: bool,
    message: String,
    output_data: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct KeyValidationResult {
    success: bool,
    message: String,
    valid: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ApiKeyInfo {
    username: String,
    email: String,
    #[serde(rename = "expiresAt")]
    expires_at: String,
    #[serde(rename = "createdAt")]
    created_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct ApiValidationResponse {
    valid: bool,
    key: Option<ApiKeyInfo>,
    reason: Option<String>,
}

// Global state to track key validation and key info
static VALIDATED_KEY: Mutex<Option<String>> = Mutex::new(None);
static KEY_INFO: Mutex<Option<ApiKeyInfo>> = Mutex::new(None);
static VALIDATION_RUNNING: Mutex<bool> = Mutex::new(false);

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn test_connection() -> String {
    println!("Test connection called from frontend!");
    "Connection successful!".to_string()
}

#[tauri::command]
async fn validate_api_key(key: String) -> Result<KeyValidationResult, String> {
    println!("Validating API key: {}", &key[..std::cmp::min(8, key.len())]);

    // API endpoint for key validation
    let api_url = format!("https://keymanager.testonly.space/api/keys/validate/{}", key);

    // Create HTTP client
    let client = reqwest::Client::new();

    match client.get(&api_url).send().await {
        Ok(response) => {
            println!("API response status: {}", response.status());

            if response.status().is_success() {
                match response.json::<ApiValidationResponse>().await {
                    Ok(api_response) => {
                        println!("API validation response: valid={}, key={:?}", api_response.valid, api_response.key);

                        if api_response.valid {
                            // Store the validated key
                            if let Ok(mut validated_key) = VALIDATED_KEY.lock() {
                                *validated_key = Some(key.clone());
                            }

                            // Store the key info
                            if let Some(key_info) = &api_response.key {
                                if let Ok(mut stored_key_info) = KEY_INFO.lock() {
                                    *stored_key_info = Some(key_info.clone());
                                }
                            }

                            let message = if let Some(key_info) = &api_response.key {
                                format!("API key validated successfully for user: {}", key_info.username)
                            } else {
                                "API key validated successfully".to_string()
                            };

                            Ok(KeyValidationResult {
                                success: true,
                                message,
                                valid: true,
                            })
                        } else {
                            let error_message = api_response.reason.unwrap_or_else(|| "Invalid API key".to_string());
                            Ok(KeyValidationResult {
                                success: true,
                                message: error_message,
                                valid: false,
                            })
                        }
                    }
                    Err(e) => {
                        println!("Failed to parse API response: {}", e);
                        Ok(KeyValidationResult {
                            success: false,
                            message: format!("Failed to parse validation response: {}", e),
                            valid: false,
                        })
                    }
                }
            } else {
                println!("API returned error status: {}", response.status());
                Ok(KeyValidationResult {
                    success: false,
                    message: format!("API validation failed with status: {}", response.status()),
                    valid: false,
                })
            }
        }
        Err(e) => {
            println!("Failed to connect to validation API: {}", e);
            Ok(KeyValidationResult {
                success: false,
                message: format!("Failed to connect to validation service: {}", e),
                valid: false,
            })
        }
    }
}

#[tauri::command]
fn check_key_status() -> bool {
    if let Ok(validated_key) = VALIDATED_KEY.lock() {
        validated_key.is_some()
    } else {
        false
    }
}

#[tauri::command]
fn get_key_info() -> Option<ApiKeyInfo> {
    if let Ok(key_info) = KEY_INFO.lock() {
        key_info.clone()
    } else {
        None
    }
}

#[tauri::command]
fn activate_new_key() -> bool {
    // Clear current key to allow new activation
    let mut success = true;

    if let Ok(mut validated_key) = VALIDATED_KEY.lock() {
        *validated_key = None;
    } else {
        success = false;
    }

    if let Ok(mut key_info) = KEY_INFO.lock() {
        *key_info = None;
    } else {
        success = false;
    }

    success
}

#[tauri::command]
fn clear_validated_key() -> bool {
    let mut success = true;

    if let Ok(mut validated_key) = VALIDATED_KEY.lock() {
        *validated_key = None;
    } else {
        success = false;
    }

    if let Ok(mut key_info) = KEY_INFO.lock() {
        *key_info = None;
    } else {
        success = false;
    }

    success
}

#[tauri::command]
async fn start_key_validation_interval(app_handle: tauri::AppHandle) -> Result<bool, String> {
    // Check if validation is already running
    if let Ok(mut running) = VALIDATION_RUNNING.lock() {
        if *running {
            return Ok(true); // Already running
        }
        *running = true;
    } else {
        return Err("Failed to access validation state".to_string());
    }

    let app_handle_clone = app_handle.clone();

    // Spawn background task for interval validation
    tokio::spawn(async move {
        let mut interval = interval(Duration::from_secs(2)); // 2 seconds

        loop {
            interval.tick().await;

            // Check if we should continue running
            let should_continue = if let Ok(running) = VALIDATION_RUNNING.lock() {
                *running
            } else {
                false
            };

            if !should_continue {
                break;
            }

            // Get current key
            let current_key = if let Ok(validated_key) = VALIDATED_KEY.lock() {
                validated_key.clone()
            } else {
                None
            };

            if let Some(key) = current_key {
                // Validate the key
                match validate_key_with_api(&key).await {
                    Ok(result) => {
                        println!("Background validation result: valid={}, message={}", result.valid, result.message);

                        if !result.valid {
                            // Key is no longer valid, clear it and emit event
                            if let Ok(mut validated_key) = VALIDATED_KEY.lock() {
                                *validated_key = None;
                            }
                            if let Ok(mut key_info) = KEY_INFO.lock() {
                                *key_info = None;
                            }

                            // Emit event to frontend
                            let _ = app_handle_clone.emit("key-invalidated", ());

                            // Stop validation loop
                            if let Ok(mut running) = VALIDATION_RUNNING.lock() {
                                *running = false;
                            }
                            break;
                        }
                    }
                    Err(e) => {
                        println!("Background validation error: {}", e);
                        // On network error, don't invalidate the key immediately
                        // Just log the error and continue
                    }
                }
            } else {
                // No key to validate, stop the loop
                if let Ok(mut running) = VALIDATION_RUNNING.lock() {
                    *running = false;
                }
                break;
            }
        }

        println!("Background key validation stopped");
    });

    Ok(true)
}

#[tauri::command]
fn stop_key_validation_interval() -> bool {
    if let Ok(mut running) = VALIDATION_RUNNING.lock() {
        *running = false;
        true
    } else {
        false
    }
}

// Helper function to validate key with API (extracted from validate_api_key)
async fn validate_key_with_api(key: &str) -> Result<KeyValidationResult, String> {
    let api_url = format!("https://keymanager.testonly.space/api/keys/validate/{}", key);
    let client = reqwest::Client::new();

    match client.get(&api_url).send().await {
        Ok(response) => {
            println!("Background API response status: {}", response.status());

            if response.status().is_success() {
                match response.json::<ApiValidationResponse>().await {
                    Ok(api_response) => {
                        println!("Background API validation response: valid={}, key={:?}, reason={:?}",
                                api_response.valid, api_response.key.is_some(), api_response.reason);

                        if api_response.valid {
                            // Update key info if provided
                            if let Some(key_info) = &api_response.key {
                                if let Ok(mut stored_key_info) = KEY_INFO.lock() {
                                    *stored_key_info = Some(key_info.clone());
                                }
                            }

                            Ok(KeyValidationResult {
                                success: true,
                                message: "Key is valid".to_string(),
                                valid: true,
                            })
                        } else {
                            let error_message = api_response.reason.unwrap_or_else(|| "Invalid API key".to_string());
                            Ok(KeyValidationResult {
                                success: true,
                                message: error_message,
                                valid: false,
                            })
                        }
                    }
                    Err(e) => {
                        println!("Background failed to parse API response: {}", e);
                        Err(format!("Failed to parse validation response: {}", e))
                    }
                }
            } else {
                println!("Background API returned error status: {}", response.status());
                Err(format!("API validation failed with status: {}", response.status()))
            }
        }
        Err(e) => {
            println!("Background failed to connect to validation API: {}", e);
            Err(format!("Failed to connect to validation service: {}", e))
        }
    }
}

#[tauri::command]
async fn restore_faces(app_handle: tauri::AppHandle, image_data: String, model_version: Option<String>, upscale: Option<i32>) -> Result<ProcessingResult, String> {
    // Check if API key is validated
    if let Ok(validated_key) = VALIDATED_KEY.lock() {
        if validated_key.is_none() {
            return Ok(ProcessingResult {
                success: false,
                message: "API key not validated. Please validate your API key first.".to_string(),
                output_data: None,
            });
        }
    } else {
        return Ok(ProcessingResult {
            success: false,
            message: "Failed to check API key validation status.".to_string(),
            output_data: None,
        });
    }

    // Try to find Python backend directory
    // First try development mode - look for project root by going up from current directory
    let current_dir = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    println!("Current directory: {:?}", current_dir);

    // In development, we're likely in src-tauri/target/debug, so go up to project root
    let mut project_root = current_dir.clone();
    let mut found_project_root = false;

    // Look for package.json to identify project root
    for _ in 0..5 {  // Try up to 5 levels up
        if project_root.join("package.json").exists() && project_root.join("python-backend").exists() {
            found_project_root = true;
            break;
        }
        if let Some(parent) = project_root.parent() {
            project_root = parent.to_path_buf();
        } else {
            break;
        }
    }

    println!("Project root search result: found={}, path={:?}", found_project_root, project_root);

    let (python_backend_dir, python_script, python_exe) = if found_project_root {
        // Development mode - use project root
        let dev_python_backend_dir = project_root.join("python-backend");
        let script = dev_python_backend_dir.join("face_restoration.py");
        let exe = if cfg!(windows) {
            dev_python_backend_dir.join("venv").join("Scripts").join("python.exe")
        } else {
            dev_python_backend_dir.join("venv").join("bin").join("python")
        };
        println!("Using development mode paths:");
        println!("  Backend dir: {:?}", dev_python_backend_dir);
        println!("  Script: {:?}", script);
        println!("  Python exe: {:?}", exe);
        (dev_python_backend_dir, script, exe)
    } else {
        // Production mode - try resource directory
        let resource_dir = app_handle.path().resource_dir()
            .map_err(|e| format!("Failed to get resource directory: {}", e))?;
        let prod_python_backend_dir = resource_dir.join("python-backend");
        let script = prod_python_backend_dir.join("face_restoration.py");
        let exe = if cfg!(windows) {
            prod_python_backend_dir.join("venv").join("Scripts").join("python.exe")
        } else {
            prod_python_backend_dir.join("venv").join("bin").join("python")
        };
        println!("Using production mode paths:");
        println!("  Backend dir: {:?}", prod_python_backend_dir);
        println!("  Script: {:?}", script);
        println!("  Python exe: {:?}", exe);
        (prod_python_backend_dir, script, exe)
    };

    // Check if Python script exists
    if !python_script.exists() {
        return Ok(ProcessingResult {
            success: false,
            message: format!("Face restoration backend not found at: {}. Please ensure GFPGAN is properly installed.", python_script.display()),
            output_data: None,
        });
    }

    // Check if virtual environment exists
    if !python_exe.exists() {
        return Ok(ProcessingResult {
            success: false,
            message: format!("Python environment not found at: {}. Please run the GFPGAN setup script first.", python_exe.display()),
            output_data: None,
        });
    }

    // Create temporary files for input and output
    let temp_dir = env::temp_dir();
    let input_id = uuid::Uuid::new_v4().to_string();
    let input_file = temp_dir.join(format!("input_{}.txt", input_id));
    let output_file = temp_dir.join(format!("output_{}.txt", input_id));

    // Write base64 data to temporary file
    fs::write(&input_file, &image_data)
        .map_err(|e| format!("Failed to write input file: {}", e))?;

    // Prepare command arguments
    let model_ver = model_version.unwrap_or_else(|| "v1.4".to_string());
    let upscale_factor = upscale.unwrap_or(2);

    // Execute Python script
    println!("Executing Python script: {:?}", python_exe);
    println!("Script path: {:?}", python_script);
    println!("Working directory: {:?}", python_backend_dir);
    println!("Model version: {}, Upscale: {} (using v1.4 by default)", model_ver, upscale_factor);

    // Use spawn instead of output to have better control over timeout
    let mut child = Command::new(&python_exe)
        .arg(&python_script)
        .arg("--base64")
        .arg("--input")
        .arg(&input_file)
        .arg("--output")
        .arg(&output_file)
        .arg("--model")
        .arg(&model_ver)
        .arg("--upscale")
        .arg(&upscale_factor.to_string())
        .current_dir(&python_backend_dir)
        .spawn()
        .map_err(|e| format!("Failed to start Python script: {}", e))?;

    // Wait for the process with a timeout (5 minutes for face restoration)
    let timeout = Duration::from_secs(300); // 5 minutes
    let start_time = std::time::Instant::now();

    let output = loop {
        match child.try_wait() {
            Ok(Some(_status)) => {
                // Process has finished
                let output = child.wait_with_output()
                    .map_err(|e| format!("Failed to read process output: {}", e))?;
                break output;
            }
            Ok(None) => {
                // Process is still running
                if start_time.elapsed() > timeout {
                    // Kill the process if it's taking too long
                    let _ = child.kill();
                    let _ = child.wait();
                    return Ok(ProcessingResult {
                        success: false,
                        message: "Face restoration timed out after 5 minutes. Try using a smaller image or check the logs.".to_string(),
                        output_data: None,
                    });
                }
                // Sleep for a short time before checking again
                std::thread::sleep(Duration::from_millis(100));
            }
            Err(e) => {
                return Err(format!("Error waiting for process: {}", e));
            }
        }
    };

    println!("Python script exit status: {}", output.status);
    if !output.stderr.is_empty() {
        println!("Python script stderr: {}", String::from_utf8_lossy(&output.stderr));
    }
    if !output.stdout.is_empty() {
        println!("Python script stdout: {}", String::from_utf8_lossy(&output.stdout));
    }

    // Clean up input file
    let _ = fs::remove_file(&input_file);

    if output.status.success() {
        // Read the result
        match fs::read_to_string(&output_file) {
            Ok(result_data) => {
                println!("Output file size: {} characters", result_data.len());
                println!("Output starts with: {}", &result_data[..std::cmp::min(50, result_data.len())]);

                // Clean up output file
                let _ = fs::remove_file(&output_file);

                let trimmed_result = result_data.trim().to_string();
                Ok(ProcessingResult {
                    success: true,
                    message: "Faces restored successfully".to_string(),
                    output_data: Some(trimmed_result),
                })
            }
            Err(e) => {
                let _ = fs::remove_file(&output_file);
                Ok(ProcessingResult {
                    success: false,
                    message: format!("Failed to read result: {}", e),
                    output_data: None,
                })
            }
        }
    } else {
        // Clean up output file
        let _ = fs::remove_file(&output_file);

        let error_msg = String::from_utf8_lossy(&output.stderr);
        Ok(ProcessingResult {
            success: false,
            message: format!("Face restoration failed: {}", error_msg),
            output_data: None,
        })
    }
}

#[tauri::command]
async fn upscale_image(app_handle: tauri::AppHandle, image_data: String, scale_factor: Option<i32>, model_name: Option<String>) -> Result<ProcessingResult, String> {
    // Check if API key is validated
    if let Ok(validated_key) = VALIDATED_KEY.lock() {
        if validated_key.is_none() {
            return Ok(ProcessingResult {
                success: false,
                message: "API key not validated. Please validate your API key first.".to_string(),
                output_data: None,
            });
        }
    } else {
        return Ok(ProcessingResult {
            success: false,
            message: "Failed to check API key validation status.".to_string(),
            output_data: None,
        });
    }

    println!("Starting image upscaling...");

    // Get the sidecar command for upscayl-ncnn
    let shell = app_handle.shell();
    let sidecar_command = shell.sidecar("upscayl-ncnn")
        .map_err(|e| format!("Failed to get upscayl-ncnn sidecar: {}", e))?;

    // Create temporary files for input and output
    let temp_dir = env::temp_dir();
    let input_id = uuid::Uuid::new_v4().to_string();
    let input_file = temp_dir.join(format!("input_{}.png", input_id));
    let output_file = temp_dir.join(format!("output_{}.png", input_id));

    // Decode base64 image data and save to temporary file
    let image_bytes = if image_data.starts_with("data:image") {
        // Remove data URL prefix if present
        let base64_data = image_data.split(',').nth(1)
            .ok_or("Invalid data URL format")?;
        base64::engine::general_purpose::STANDARD.decode(base64_data)
            .map_err(|e| format!("Failed to decode base64: {}", e))?
    } else {
        base64::engine::general_purpose::STANDARD.decode(&image_data)
            .map_err(|e| format!("Failed to decode base64: {}", e))?
    };

    fs::write(&input_file, &image_bytes)
        .map_err(|e| format!("Failed to write input file: {}", e))?;

    // Set default values
    let scale = scale_factor.unwrap_or(4);
    let model = model_name.unwrap_or_else(|| "realesrgan-x4plus".to_string());

    println!("Upscaling with scale: {}, model: {}", scale, model);
    println!("Input file: {:?}", input_file);
    println!("Output file: {:?}", output_file);

    // Get the models directory path - check development vs production mode
    let current_dir = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    println!("Current directory: {:?}", current_dir);

    // In development, we're likely in src-tauri/target/debug, so go up to project root
    let mut project_root = current_dir.clone();
    let mut found_project_root = false;

    // Look for package.json to identify project root
    for _ in 0..5 {  // Try up to 5 levels up
        if project_root.join("package.json").exists() && project_root.join("src-tauri").join("binaries").exists() {
            found_project_root = true;
            break;
        }
        if let Some(parent) = project_root.parent() {
            project_root = parent.to_path_buf();
        } else {
            break;
        }
    }

    let models_dir = if found_project_root {
        // Development mode - use project root
        let dev_models_dir = project_root.join("src-tauri").join("binaries").join("models");
        println!("Using development mode models path: {:?}", dev_models_dir);
        dev_models_dir
    } else {
        // Production mode - try resource directory
        let resource_dir = app_handle.path().resource_dir()
            .map_err(|e| format!("Failed to get resource directory: {}", e))?;
        let prod_models_dir = resource_dir.join("binaries").join("models");
        println!("Using production mode models path: {:?}", prod_models_dir);
        prod_models_dir
    };

    // Check if models directory exists
    if !models_dir.exists() {
        return Ok(ProcessingResult {
            success: false,
            message: format!("Models directory not found at: {}. Please ensure the upscaling models are properly installed.", models_dir.display()),
            output_data: None,
        });
    }

    println!("Models directory: {:?}", models_dir);

    // Execute upscayl-ncnn
    let output = sidecar_command
        .args([
            "-i", input_file.to_str().unwrap(),
            "-o", output_file.to_str().unwrap(),
            "-s", &scale.to_string(),
            "-n", &model,
            "-m", models_dir.to_str().unwrap(),
        ])
        .output()
        .await
        .map_err(|e| format!("Failed to execute upscayl-ncnn: {}", e))?;

    println!("Upscayl exit status: {:?}", output.status);
    if !output.stderr.is_empty() {
        println!("Upscayl stderr: {}", String::from_utf8_lossy(&output.stderr));
    }
    if !output.stdout.is_empty() {
        println!("Upscayl stdout: {}", String::from_utf8_lossy(&output.stdout));
    }

    // Clean up input file
    let _ = fs::remove_file(&input_file);

    if output.status.success() {
        // Read the output image and convert to base64
        match fs::read(&output_file) {
            Ok(output_bytes) => {
                // Clean up output file
                let _ = fs::remove_file(&output_file);

                let output_base64 = base64::engine::general_purpose::STANDARD.encode(&output_bytes);
                let result_data = format!("data:image/png;base64,{}", output_base64);

                Ok(ProcessingResult {
                    success: true,
                    message: format!("Image upscaled successfully ({}x)", scale),
                    output_data: Some(result_data),
                })
            }
            Err(e) => {
                let _ = fs::remove_file(&output_file);
                Ok(ProcessingResult {
                    success: false,
                    message: format!("Failed to read upscaled image: {}", e),
                    output_data: None,
                })
            }
        }
    } else {
        // Clean up output file
        let _ = fs::remove_file(&output_file);

        let error_msg = String::from_utf8_lossy(&output.stderr);
        Ok(ProcessingResult {
            success: false,
            message: format!("Upscaling failed: {}", error_msg),
            output_data: None,
        })
    }
}

#[tauri::command]
async fn remove_background(app_handle: tauri::AppHandle, image_data: String) -> Result<ProcessingResult, String> {
    // Check if API key is validated
    if let Ok(validated_key) = VALIDATED_KEY.lock() {
        if validated_key.is_none() {
            return Ok(ProcessingResult {
                success: false,
                message: "API key not validated. Please validate your API key first.".to_string(),
                output_data: None,
            });
        }
    } else {
        return Ok(ProcessingResult {
            success: false,
            message: "Failed to check API key validation status.".to_string(),
            output_data: None,
        });
    }

    // Try to find Python backend directory
    // First try development mode - look for project root by going up from current directory
    let current_dir = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    println!("Current directory: {:?}", current_dir);

    // In development, we're likely in src-tauri/target/debug, so go up to project root
    let mut project_root = current_dir.clone();
    let mut found_project_root = false;

    // Look for package.json to identify project root
    for _ in 0..5 {  // Try up to 5 levels up
        if project_root.join("package.json").exists() && project_root.join("python-backend").exists() {
            found_project_root = true;
            break;
        }
        if let Some(parent) = project_root.parent() {
            project_root = parent.to_path_buf();
        } else {
            break;
        }
    }

    println!("Project root search result: found={}, path={:?}", found_project_root, project_root);

    let (python_backend_dir, python_script, python_exe) = if found_project_root {
        // Development mode - use project root
        let dev_python_backend_dir = project_root.join("python-backend");
        let script = dev_python_backend_dir.join("remove_bg.py");
        let exe = if cfg!(windows) {
            dev_python_backend_dir.join("venv").join("Scripts").join("python.exe")
        } else {
            dev_python_backend_dir.join("venv").join("bin").join("python")
        };
        println!("Using development mode paths:");
        println!("  Backend dir: {:?}", dev_python_backend_dir);
        println!("  Script: {:?}", script);
        println!("  Python exe: {:?}", exe);
        (dev_python_backend_dir, script, exe)
    } else {
        // Production mode - try resource directory
        let resource_dir = app_handle.path().resource_dir()
            .map_err(|e| format!("Failed to get resource directory: {}", e))?;
        let prod_python_backend_dir = resource_dir.join("python-backend");
        let script = prod_python_backend_dir.join("remove_bg.py");
        let exe = if cfg!(windows) {
            prod_python_backend_dir.join("venv").join("Scripts").join("python.exe")
        } else {
            prod_python_backend_dir.join("venv").join("bin").join("python")
        };
        println!("Using production mode paths:");
        println!("  Backend dir: {:?}", prod_python_backend_dir);
        println!("  Script: {:?}", script);
        println!("  Python exe: {:?}", exe);
        (prod_python_backend_dir, script, exe)
    };

    // Check if Python script exists
    if !python_script.exists() {
        return Ok(ProcessingResult {
            success: false,
            message: format!("Python backend not found at: {}. Please ensure the application is properly installed.", python_script.display()),
            output_data: None,
        });
    }

    // Check if virtual environment exists
    if !python_exe.exists() {
        return Ok(ProcessingResult {
            success: false,
            message: format!("Python environment not found at: {}. Please run the setup script first.", python_exe.display()),
            output_data: None,
        });
    }

    // Create temporary files for input and output
    let temp_dir = env::temp_dir();
    let input_id = uuid::Uuid::new_v4().to_string();
    let input_file = temp_dir.join(format!("input_{}.txt", input_id));
    let output_file = temp_dir.join(format!("output_{}.txt", input_id));

    // Write base64 data to temporary file
    fs::write(&input_file, &image_data)
        .map_err(|e| format!("Failed to write input file: {}", e))?;

    // Execute Python script
    println!("Executing Python script: {:?}", python_exe);
    println!("Script path: {:?}", python_script);
    println!("Working directory: {:?}", python_backend_dir);

    let output = Command::new(&python_exe)
        .arg(&python_script)
        .arg("--base64")
        .arg("--input")
        .arg(&input_file)
        .arg("--output")
        .arg(&output_file)
        .current_dir(&python_backend_dir)
        .output()
        .map_err(|e| format!("Failed to execute Python script: {}", e))?;

    println!("Python script exit status: {}", output.status);
    if !output.stderr.is_empty() {
        println!("Python script stderr: {}", String::from_utf8_lossy(&output.stderr));
    }
    if !output.stdout.is_empty() {
        println!("Python script stdout: {}", String::from_utf8_lossy(&output.stdout));
    }

    // Clean up input file
    let _ = fs::remove_file(&input_file);

    if output.status.success() {
        // Read the result
        match fs::read_to_string(&output_file) {
            Ok(result_data) => {
                println!("Output file size: {} characters", result_data.len());
                println!("Output starts with: {}", &result_data[..std::cmp::min(50, result_data.len())]);

                // Clean up output file
                let _ = fs::remove_file(&output_file);

                let trimmed_result = result_data.trim().to_string();
                Ok(ProcessingResult {
                    success: true,
                    message: "Background removed successfully".to_string(),
                    output_data: Some(trimmed_result),
                })
            }
            Err(e) => {
                let _ = fs::remove_file(&output_file);
                Ok(ProcessingResult {
                    success: false,
                    message: format!("Failed to read result: {}", e),
                    output_data: None,
                })
            }
        }
    } else {
        // Clean up output file
        let _ = fs::remove_file(&output_file);

        let error_msg = String::from_utf8_lossy(&output.stderr);
        Ok(ProcessingResult {
            success: false,
            message: format!("Python script failed: {}", error_msg),
            output_data: None,
        })
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_shell::init())
        .invoke_handler(tauri::generate_handler![greet, test_connection, validate_api_key, check_key_status, get_key_info, activate_new_key, clear_validated_key, start_key_validation_interval, stop_key_validation_interval, remove_background, restore_faces, upscale_image])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
