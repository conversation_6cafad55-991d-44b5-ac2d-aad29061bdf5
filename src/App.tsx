import { useState, useEffect } from "react";
import { invoke } from "@tauri-apps/api/core";
import { listen } from "@tauri-apps/api/event";
import "./App.css";
import Sidebar from "./containers/sidebar";
import ImageProcessingStudio from "./components/ImageProcessingStudio";
import KeyValidation from "./components/KeyValidation";
import KeyInfoPage from "./components/KeyInfoPage";

// Helper function to check if we're running in Tauri context
const isTauriContext = () => {
	return typeof window !== 'undefined' && (
		(window as any).__TAURI__ !== undefined ||
		(window as any).__TAURI_INTERNALS__ !== undefined ||
		typeof invoke !== 'undefined'
	);
};

type Page = 'main' | 'keyInfo';

function App() {
	const [isKeyValidated, setIsKeyValidated] = useState(false);
	const [isCheckingKey, setIsCheckingKey] = useState(true);
	const [currentPage, setCurrentPage] = useState<Page>('main');

	// Backend interval validation
	const startKeyValidationInterval = async () => {
		if (!isTauriContext()) return;

		try {
			await invoke("start_key_validation_interval");
			console.log("Backend key validation started");
		} catch (error) {
			console.error("Failed to start backend validation:", error);
		}
	};

	const stopKeyValidationInterval = async () => {
		if (!isTauriContext()) return;

		try {
			await invoke("stop_key_validation_interval");
			console.log("Backend key validation stopped");
		} catch (error) {
			console.error("Failed to stop backend validation:", error);
		}
	};

	useEffect(() => {
		// Check if we're in Tauri context and if key is already validated
		const checkKeyStatus = async () => {
			if (!isTauriContext()) {
				// In development mode, skip key validation
				setIsKeyValidated(true);
				setIsCheckingKey(false);
				return;
			}

			try {
				const isValidated = await invoke<boolean>("check_key_status");
				setIsKeyValidated(isValidated);
				if (isValidated) {
					await startKeyValidationInterval();
				}
			} catch (error) {
				console.error("Failed to check key status:", error);
				setIsKeyValidated(false);
			} finally {
				setIsCheckingKey(false);
			}
		};

		checkKeyStatus();

		// Cleanup on unmount
		return () => {
			stopKeyValidationInterval();
		};
	}, []);

	// Listen for key invalidation events from backend
	useEffect(() => {
		if (!isTauriContext() || !isKeyValidated) return;

		const setupEventListener = async () => {
			const unlisten = await listen("key-invalidated", () => {
				console.log("Key invalidated by backend, logging out user");
				setIsKeyValidated(false);
				setCurrentPage('main');
			});

			return unlisten;
		};

		let unlistenPromise = setupEventListener();

		return () => {
			unlistenPromise.then(unlisten => unlisten());
		};
	}, [isKeyValidated]);

	const handleValidationSuccess = async () => {
		setIsKeyValidated(true);
		setCurrentPage('main');
		await startKeyValidationInterval();
	};

	const handleShowKeyInfo = () => {
		setCurrentPage('keyInfo');
	};

	const handleBackToMain = () => {
		setCurrentPage('main');
	};

	const handleActivateNewKey = async () => {
		await stopKeyValidationInterval();
		setIsKeyValidated(false);
		setCurrentPage('main');
	};

	const handleLogout = async () => {
		await stopKeyValidationInterval();
		setIsKeyValidated(false);
		setCurrentPage('main');
	};

	// Show loading while checking key status
	if (isCheckingKey) {
		return (
			<div style={{
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				height: '100vh',
				fontSize: '18px',
				color: '#6b7280'
			}}>
				Checking API key status...
			</div>
		);
	}

	// Show key validation dialog if not validated
	if (!isKeyValidated) {
		return <KeyValidation onValidationSuccess={handleValidationSuccess} />;
	}

	// Show key info page
	if (currentPage === 'keyInfo') {
		return (
			<KeyInfoPage
				onActivateNewKey={handleActivateNewKey}
				onLogout={handleLogout}
				onBack={handleBackToMain}
			/>
		);
	}

	// Show main app if key is validated
	return (
		<>
			<Sidebar onShowKeyInfo={handleShowKeyInfo} />
			<main style={{ marginLeft: 200 }}>
				<ImageProcessingStudio />
			</main>
		</>
	);
}

export default App;
