import { useState, useEffect } from "react";
import { invoke } from "@tauri-apps/api/core";
import "./App.css";
import Sidebar from "./containers/sidebar";
import ImageProcessingStudio from "./components/ImageProcessingStudio";
import KeyValidation from "./components/KeyValidation";

// Helper function to check if we're running in Tauri context
const isTauriContext = () => {
	return typeof window !== 'undefined' && (
		(window as any).__TAURI__ !== undefined ||
		(window as any).__TAURI_INTERNALS__ !== undefined ||
		typeof invoke !== 'undefined'
	);
};

function App() {
	const [isKeyValidated, setIsKeyValidated] = useState(false);
	const [isCheckingKey, setIsCheckingKey] = useState(true);

	useEffect(() => {
		// Check if we're in Tauri context and if key is already validated
		const checkKeyStatus = async () => {
			if (!isTauriContext()) {
				// In development mode, skip key validation
				setIsKeyValidated(true);
				setIsCheckingKey(false);
				return;
			}

			try {
				const isValidated = await invoke<boolean>("check_key_status");
				setIsKeyValidated(isValidated);
			} catch (error) {
				console.error("Failed to check key status:", error);
				setIsKeyValidated(false);
			} finally {
				setIsCheckingKey(false);
			}
		};

		checkKeyStatus();
	}, []);

	const handleValidationSuccess = () => {
		setIsKeyValidated(true);
	};

	// Show loading while checking key status
	if (isCheckingKey) {
		return (
			<div style={{
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				height: '100vh',
				fontSize: '18px',
				color: '#6b7280'
			}}>
				Checking API key status...
			</div>
		);
	}

	// Show key validation dialog if not validated
	if (!isKeyValidated) {
		return <KeyValidation onValidationSuccess={handleValidationSuccess} />;
	}

	// Show main app if key is validated
	return (
		<>
			<Sidebar />
			<main style={{ marginLeft: 200 }}>
				<ImageProcessingStudio />
			</main>
		</>
	);
}

export default App;
