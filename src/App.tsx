import { useState, useEffect, useRef } from "react";
import { invoke } from "@tauri-apps/api/core";
import "./App.css";
import Sidebar from "./containers/sidebar";
import ImageProcessingStudio from "./components/ImageProcessingStudio";
import KeyValidation from "./components/KeyValidation";
import KeyInfoPage from "./components/KeyInfoPage";

// Helper function to check if we're running in Tauri context
const isTauriContext = () => {
	return typeof window !== 'undefined' && (
		(window as any).__TAURI__ !== undefined ||
		(window as any).__TAURI_INTERNALS__ !== undefined ||
		typeof invoke !== 'undefined'
	);
};

type Page = 'main' | 'keyInfo';

function App() {
	const [isKeyValidated, setIsKeyValidated] = useState(false);
	const [isCheckingKey, setIsCheckingKey] = useState(true);
	const [currentPage, setCurrentPage] = useState<Page>('main');
	const intervalRef = useRef<number | null>(null);

	// Interval validation check
	const startKeyValidationInterval = () => {
		if (!isTauriContext()) return;

		// Clear existing interval
		if (intervalRef.current) {
			clearInterval(intervalRef.current);
		}

		// Check key validity every 5 minutes
		intervalRef.current = setInterval(async () => {
			try {
				const isValidated = await invoke<boolean>("check_key_status");
				if (!isValidated) {
					console.log("Key validation lost, logging out user");
					setIsKeyValidated(false);
					setCurrentPage('main');
				}
			} catch (error) {
				console.error("Failed to check key status during interval:", error);
				setIsKeyValidated(false);
				setCurrentPage('main');
			}
		}, 5 * 60 * 1000); // 5 minutes
	};

	const stopKeyValidationInterval = () => {
		if (intervalRef.current) {
			clearInterval(intervalRef.current);
			intervalRef.current = null;
		}
	};

	useEffect(() => {
		// Check if we're in Tauri context and if key is already validated
		const checkKeyStatus = async () => {
			if (!isTauriContext()) {
				// In development mode, skip key validation
				setIsKeyValidated(true);
				setIsCheckingKey(false);
				return;
			}

			try {
				const isValidated = await invoke<boolean>("check_key_status");
				setIsKeyValidated(isValidated);
				if (isValidated) {
					startKeyValidationInterval();
				}
			} catch (error) {
				console.error("Failed to check key status:", error);
				setIsKeyValidated(false);
			} finally {
				setIsCheckingKey(false);
			}
		};

		checkKeyStatus();

		// Cleanup interval on unmount
		return () => {
			stopKeyValidationInterval();
		};
	}, []);

	const handleValidationSuccess = () => {
		setIsKeyValidated(true);
		setCurrentPage('main');
		startKeyValidationInterval();
	};

	const handleShowKeyInfo = () => {
		setCurrentPage('keyInfo');
	};

	const handleActivateNewKey = () => {
		setIsKeyValidated(false);
		setCurrentPage('main');
		stopKeyValidationInterval();
	};

	const handleLogout = () => {
		setIsKeyValidated(false);
		setCurrentPage('main');
		stopKeyValidationInterval();
	};

	// Show loading while checking key status
	if (isCheckingKey) {
		return (
			<div style={{
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				height: '100vh',
				fontSize: '18px',
				color: '#6b7280'
			}}>
				Checking API key status...
			</div>
		);
	}

	// Show key validation dialog if not validated
	if (!isKeyValidated) {
		return <KeyValidation onValidationSuccess={handleValidationSuccess} />;
	}

	// Show key info page
	if (currentPage === 'keyInfo') {
		return (
			<KeyInfoPage
				onActivateNewKey={handleActivateNewKey}
				onLogout={handleLogout}
			/>
		);
	}

	// Show main app if key is validated
	return (
		<>
			<Sidebar onShowKeyInfo={handleShowKeyInfo} />
			<main style={{ marginLeft: 200 }}>
				<ImageProcessingStudio />
			</main>
		</>
	);
}

export default App;
