import { useState, useEffect } from "react";
import { Tb<PERSON><PERSON>, TbX, Tb<PERSON>ser, TbMail, TbCalendar, Tb<PERSON>lock } from "react-icons/tb";
import { css } from "styled-system/css";
import { hstack, vstack } from "styled-system/patterns";
import { invoke } from "@tauri-apps/api/core";

const overlayStyle = css({
	position: "fixed",
	top: 0,
	left: 0,
	right: 0,
	bottom: 0,
	backgroundColor: "rgba(0, 0, 0, 0.5)",
	display: "flex",
	alignItems: "center",
	justifyContent: "center",
	zIndex: 1000,
});

const modalStyle = css({
	backgroundColor: "white",
	borderRadius: "12px",
	padding: "32px",
	maxWidth: "500px",
	width: "90%",
	boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
	position: "relative",
});

const closeButtonStyle = css({
	position: "absolute",
	top: "16px",
	right: "16px",
	backgroundColor: "transparent",
	border: "none",
	cursor: "pointer",
	padding: "8px",
	borderRadius: "6px",
	color: "#6b7280",
	"&:hover": {
		backgroundColor: "#f3f4f6",
		color: "#374151",
	},
});

const headerStyle = css({
	textAlign: "center",
	marginBottom: "32px",
});

const titleStyle = css({
	fontSize: "24px",
	fontWeight: "700",
	color: "#111827",
	marginBottom: "8px",
});

const subtitleStyle = css({
	fontSize: "16px",
	color: "#6b7280",
	lineHeight: "1.5",
});

const infoSectionStyle = vstack({
	gap: "20px",
	alignItems: "stretch",
});

const infoItemStyle = css({
	backgroundColor: "#f9fafb",
	border: "1px solid #e5e7eb",
	borderRadius: "8px",
	padding: "16px",
});

const infoLabelStyle = css({
	fontSize: "12px",
	fontWeight: "600",
	color: "#6b7280",
	textTransform: "uppercase",
	letterSpacing: "0.05em",
	marginBottom: "8px",
});

const infoValueStyle = css({
	fontSize: "16px",
	fontWeight: "500",
	color: "#111827",
	wordBreak: "break-all",
});

const statusBadgeStyle = css({
	display: "inline-flex",
	alignItems: "center",
	gap: "6px",
	backgroundColor: "#d1fae5",
	color: "#065f46",
	padding: "6px 12px",
	borderRadius: "20px",
	fontSize: "14px",
	fontWeight: "600",
	border: "1px solid #a7f3d0",
});

interface ApiKeyInfo {
	username: string;
	email: string;
	expires_at: string;
	created_at: string;
}

interface KeyInfoModalProps {
	onClose: () => void;
}

const KeyInfoModal = ({ onClose }: KeyInfoModalProps) => {
	const [keyInfo, setKeyInfo] = useState<ApiKeyInfo | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		const fetchKeyInfo = async () => {
			try {
				const info = await invoke<ApiKeyInfo | null>("get_key_info");
				setKeyInfo(info);
			} catch (error) {
				console.error("Failed to fetch key info:", error);
			} finally {
				setIsLoading(false);
			}
		};

		fetchKeyInfo();
	}, []);

	const formatDate = (dateString: string) => {
		try {
			const date = new Date(dateString);
			return date.toLocaleDateString("en-US", {
				year: "numeric",
				month: "long",
				day: "numeric",
				hour: "2-digit",
				minute: "2-digit",
			});
		} catch {
			return dateString;
		}
	};

	const isExpired = (expiresAt: string) => {
		try {
			const expiryDate = new Date(expiresAt);
			return expiryDate < new Date();
		} catch {
			return false;
		}
	};

	const handleOverlayClick = (e: React.MouseEvent) => {
		if (e.target === e.currentTarget) {
			onClose();
		}
	};

	return (
		<div className={overlayStyle} onClick={handleOverlayClick}>
			<div className={modalStyle}>
				<button onClick={onClose} className={closeButtonStyle}>
					<TbX size={20} />
				</button>

				<div className={headerStyle}>
					<div className={hstack({ gap: "12px", justifyContent: "center", marginBottom: "16px" })}>
						<div className={css({
							backgroundColor: "#10b981",
							borderRadius: "12px",
							padding: "12px",
						})}>
							<TbKey size={32} className={css({ color: "white" })} />
						</div>
					</div>
					<h2 className={titleStyle}>API Key Information</h2>
					<p className={subtitleStyle}>
						Details about your currently active API key
					</p>
				</div>

				{isLoading ? (
					<div className={css({
						textAlign: "center",
						padding: "40px",
						color: "#6b7280",
					})}>
						Loading key information...
					</div>
				) : keyInfo ? (
					<div className={infoSectionStyle}>
						<div className={css({ textAlign: "center", marginBottom: "20px" })}>
							<div className={statusBadgeStyle}>
								<TbKey size={16} />
								{isExpired(keyInfo.expires_at) ? "Expired" : "Active"}
							</div>
						</div>

						<div className={infoItemStyle}>
							<div className={hstack({ gap: "8px", marginBottom: "8px" })}>
								<TbUser size={16} className={css({ color: "#6b7280" })} />
								<div className={infoLabelStyle}>Username</div>
							</div>
							<div className={infoValueStyle}>{keyInfo.username}</div>
						</div>

						<div className={infoItemStyle}>
							<div className={hstack({ gap: "8px", marginBottom: "8px" })}>
								<TbMail size={16} className={css({ color: "#6b7280" })} />
								<div className={infoLabelStyle}>Email</div>
							</div>
							<div className={infoValueStyle}>{keyInfo.email}</div>
						</div>

						<div className={infoItemStyle}>
							<div className={hstack({ gap: "8px", marginBottom: "8px" })}>
								<TbCalendar size={16} className={css({ color: "#6b7280" })} />
								<div className={infoLabelStyle}>Expires At</div>
							</div>
							<div className={css({
								fontSize: "16px",
								fontWeight: "500",
								wordBreak: "break-all",
								color: isExpired(keyInfo.expires_at) ? "#dc2626" : "#111827",
							})}>
								{formatDate(keyInfo.expires_at)}
							</div>
						</div>

						<div className={infoItemStyle}>
							<div className={hstack({ gap: "8px", marginBottom: "8px" })}>
								<TbClock size={16} className={css({ color: "#6b7280" })} />
								<div className={infoLabelStyle}>Created At</div>
							</div>
							<div className={infoValueStyle}>{formatDate(keyInfo.created_at)}</div>
						</div>
					</div>
				) : (
					<div className={css({
						textAlign: "center",
						padding: "40px",
						color: "#ef4444",
					})}>
						No key information available
					</div>
				)}
			</div>
		</div>
	);
};

export default KeyInfoModal;
