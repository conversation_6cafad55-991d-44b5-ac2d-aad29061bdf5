import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, <PERSON>bMail, TbCalendar, <PERSON>b<PERSON>lock, TbRefresh, TbLogout } from "react-icons/tb";
import { css } from "styled-system/css";
import { hstack, vstack } from "styled-system/patterns";
import { invoke } from "@tauri-apps/api/core";

const pageStyle = css({
	padding: "32px",
	maxWidth: "800px",
	margin: "0 auto",
	minHeight: "100vh",
	backgroundColor: "#f9fafb",
});

const headerStyle = css({
	backgroundColor: "white",
	borderRadius: "12px",
	padding: "32px",
	marginBottom: "32px",
	boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
});

const titleStyle = css({
	fontSize: "32px",
	fontWeight: "700",
	color: "#111827",
	marginBottom: "8px",
});

const subtitleStyle = css({
	fontSize: "18px",
	color: "#6b7280",
	lineHeight: "1.5",
});

const contentStyle = css({
	backgroundColor: "white",
	borderRadius: "12px",
	padding: "32px",
	boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
});

const infoSectionStyle = vstack({
	gap: "24px",
	alignItems: "stretch",
});

const infoItemStyle = css({
	backgroundColor: "#f9fafb",
	border: "1px solid #e5e7eb",
	borderRadius: "8px",
	padding: "20px",
});

const infoLabelStyle = css({
	fontSize: "14px",
	fontWeight: "600",
	color: "#6b7280",
	textTransform: "uppercase",
	letterSpacing: "0.05em",
	marginBottom: "8px",
});

const infoValueStyle = css({
	fontSize: "18px",
	fontWeight: "500",
	color: "#111827",
	wordBreak: "break-all",
});

const statusBadgeStyle = css({
	display: "inline-flex",
	alignItems: "center",
	gap: "8px",
	backgroundColor: "#d1fae5",
	color: "#065f46",
	padding: "8px 16px",
	borderRadius: "20px",
	fontSize: "16px",
	fontWeight: "600",
	border: "1px solid #a7f3d0",
	marginBottom: "24px",
});

const expiredBadgeStyle = css({
	display: "inline-flex",
	alignItems: "center",
	gap: "8px",
	backgroundColor: "#fee2e2",
	color: "#dc2626",
	padding: "8px 16px",
	borderRadius: "20px",
	fontSize: "16px",
	fontWeight: "600",
	border: "1px solid #fca5a5",
	marginBottom: "24px",
});

const primaryButtonStyle = css({
	padding: "12px 24px",
	borderRadius: "8px",
	border: "none",
	fontSize: "16px",
	fontWeight: "600",
	cursor: "pointer",
	transition: "all 0.2s",
	display: "flex",
	alignItems: "center",
	justifyContent: "center",
	gap: "8px",
	backgroundColor: "#6366f1",
	color: "white",
	"&:hover": {
		backgroundColor: "#5856eb",
	},
});

const dangerButtonStyle = css({
	padding: "12px 24px",
	borderRadius: "8px",
	border: "none",
	fontSize: "16px",
	fontWeight: "600",
	cursor: "pointer",
	transition: "all 0.2s",
	display: "flex",
	alignItems: "center",
	justifyContent: "center",
	gap: "8px",
	backgroundColor: "#ef4444",
	color: "white",
	"&:hover": {
		backgroundColor: "#dc2626",
	},
});

const loadingStyle = css({
	textAlign: "center",
	padding: "60px",
	color: "#6b7280",
	fontSize: "18px",
});

interface ApiKeyInfo {
	username: string;
	email: string;
	expires_at: string;
	created_at: string;
}

interface KeyInfoPageProps {
	onActivateNewKey: () => void;
	onLogout: () => void;
}

const KeyInfoPage = ({ onActivateNewKey, onLogout }: KeyInfoPageProps) => {
	const [keyInfo, setKeyInfo] = useState<ApiKeyInfo | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	const fetchKeyInfo = async () => {
		try {
			const info = await invoke<ApiKeyInfo | null>("get_key_info");
			setKeyInfo(info);
		} catch (error) {
			console.error("Failed to fetch key info:", error);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchKeyInfo();
	}, []);

	const formatDate = (dateString: string) => {
		try {
			const date = new Date(dateString);
			if (isNaN(date.getTime())) {
				return "Invalid date";
			}
			return date.toLocaleDateString("en-US", {
				year: "numeric",
				month: "long",
				day: "numeric",
				hour: "2-digit",
				minute: "2-digit",
				timeZoneName: "short",
			});
		} catch {
			return "Invalid date";
		}
	};

	const isExpired = (expiresAt: string) => {
		try {
			const expiryDate = new Date(expiresAt);
			if (isNaN(expiryDate.getTime())) {
				return false;
			}
			return expiryDate < new Date();
		} catch {
			return false;
		}
	};

	const handleActivateNewKey = async () => {
		try {
			await invoke("activate_new_key");
			onActivateNewKey();
		} catch (error) {
			console.error("Failed to activate new key:", error);
		}
	};

	const handleLogout = async () => {
		try {
			await invoke("clear_validated_key");
			onLogout();
		} catch (error) {
			console.error("Failed to logout:", error);
		}
	};

	if (isLoading) {
		return (
			<div className={pageStyle}>
				<div className={loadingStyle}>
					Loading API key information...
				</div>
			</div>
		);
	}

	if (!keyInfo) {
		return (
			<div className={pageStyle}>
				<div className={loadingStyle}>
					No API key information available
				</div>
			</div>
		);
	}

	const expired = isExpired(keyInfo.expires_at);

	return (
		<div className={pageStyle}>
			<div className={headerStyle}>
				<div className={hstack({ gap: "16px", alignItems: "center", marginBottom: "16px" })}>
					<div className={css({
						backgroundColor: expired ? "#ef4444" : "#10b981",
						borderRadius: "12px",
						padding: "12px",
					})}>
						<TbKey size={32} className={css({ color: "white" })} />
					</div>
					<div>
						<h1 className={titleStyle}>API Key Information</h1>
						<p className={subtitleStyle}>
							Manage your API key and view account details
						</p>
					</div>
				</div>

				<div className={expired ? expiredBadgeStyle : statusBadgeStyle}>
					<TbKey size={20} />
					{expired ? "Key Expired" : "Key Active"}
				</div>
			</div>

			<div className={contentStyle}>
				<div className={infoSectionStyle}>
					<div className={infoItemStyle}>
						<div className={hstack({ gap: "8px", marginBottom: "8px" })}>
							<TbUser size={20} className={css({ color: "#6b7280" })} />
							<div className={infoLabelStyle}>Username</div>
						</div>
						<div className={infoValueStyle}>{keyInfo.username}</div>
					</div>

					<div className={infoItemStyle}>
						<div className={hstack({ gap: "8px", marginBottom: "8px" })}>
							<TbMail size={20} className={css({ color: "#6b7280" })} />
							<div className={infoLabelStyle}>Email</div>
						</div>
						<div className={infoValueStyle}>{keyInfo.email}</div>
					</div>

					<div className={infoItemStyle}>
						<div className={hstack({ gap: "8px", marginBottom: "8px" })}>
							<TbCalendar size={20} className={css({ color: "#6b7280" })} />
							<div className={infoLabelStyle}>Expires At</div>
						</div>
						<div className={css({
							fontSize: "18px",
							fontWeight: "500",
							wordBreak: "break-all",
							color: expired ? "#dc2626" : "#111827",
						})}>
							{formatDate(keyInfo.expires_at)}
						</div>
					</div>

					<div className={infoItemStyle}>
						<div className={hstack({ gap: "8px", marginBottom: "8px" })}>
							<TbClock size={20} className={css({ color: "#6b7280" })} />
							<div className={infoLabelStyle}>Created At</div>
						</div>
						<div className={infoValueStyle}>{formatDate(keyInfo.created_at)}</div>
					</div>
				</div>

				<div className={hstack({ gap: "16px", marginTop: "32px", justifyContent: "center" })}>
					<button onClick={handleActivateNewKey} className={primaryButtonStyle}>
						<TbRefresh size={20} />
						Activate New Key
					</button>
					<button onClick={handleLogout} className={dangerButtonStyle}>
						<TbLogout size={20} />
						Logout
					</button>
				</div>
			</div>
		</div>
	);
};

export default KeyInfoPage;
