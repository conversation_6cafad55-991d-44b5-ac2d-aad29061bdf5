import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, <PERSON>bMail, TbCalendar, Tb<PERSON>lock, TbRefresh, TbLogout, TbArrowLeft } from "react-icons/tb";
import { css } from "styled-system/css";
import { hstack, vstack } from "styled-system/patterns";
import { invoke } from "@tauri-apps/api/core";

const pageStyle = css({
	padding: "16px",
	maxWidth: "600px",
	margin: "0 auto",
	minHeight: "100vh",
	backgroundColor: "#f9fafb",
});

const headerStyle = css({
	backgroundColor: "white",
	borderRadius: "8px",
	padding: "20px",
	marginBottom: "16px",
	boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
});

const titleStyle = css({
	fontSize: "24px",
	fontWeight: "700",
	color: "#111827",
	marginBottom: "4px",
});

const subtitleStyle = css({
	fontSize: "14px",
	color: "#6b7280",
	lineHeight: "1.4",
});

const contentStyle = css({
	backgroundColor: "white",
	borderRadius: "8px",
	padding: "20px",
	boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
});

const infoSectionStyle = vstack({
	gap: "12px",
	alignItems: "stretch",
});

const infoItemStyle = css({
	backgroundColor: "#f9fafb",
	border: "1px solid #e5e7eb",
	borderRadius: "6px",
	padding: "12px",
});

const infoLabelStyle = css({
	fontSize: "12px",
	fontWeight: "600",
	color: "#6b7280",
	textTransform: "uppercase",
	letterSpacing: "0.05em",
	marginBottom: "4px",
});

const infoValueStyle = css({
	fontSize: "14px",
	fontWeight: "500",
	color: "#111827",
	wordBreak: "break-all",
});

const statusBadgeStyle = css({
	display: "inline-flex",
	alignItems: "center",
	gap: "6px",
	backgroundColor: "#d1fae5",
	color: "#065f46",
	padding: "6px 12px",
	borderRadius: "16px",
	fontSize: "14px",
	fontWeight: "600",
	border: "1px solid #a7f3d0",
	marginBottom: "16px",
});

const expiredBadgeStyle = css({
	display: "inline-flex",
	alignItems: "center",
	gap: "6px",
	backgroundColor: "#fee2e2",
	color: "#dc2626",
	padding: "6px 12px",
	borderRadius: "16px",
	fontSize: "14px",
	fontWeight: "600",
	border: "1px solid #fca5a5",
	marginBottom: "16px",
});

const primaryButtonStyle = css({
	padding: "8px 16px",
	borderRadius: "6px",
	border: "none",
	fontSize: "14px",
	fontWeight: "600",
	cursor: "pointer",
	transition: "all 0.2s",
	display: "flex",
	alignItems: "center",
	justifyContent: "center",
	gap: "6px",
	backgroundColor: "#6366f1",
	color: "white",
	"&:hover": {
		backgroundColor: "#5856eb",
	},
});

const dangerButtonStyle = css({
	padding: "8px 16px",
	borderRadius: "6px",
	border: "none",
	fontSize: "14px",
	fontWeight: "600",
	cursor: "pointer",
	transition: "all 0.2s",
	display: "flex",
	alignItems: "center",
	justifyContent: "center",
	gap: "6px",
	backgroundColor: "#ef4444",
	color: "white",
	"&:hover": {
		backgroundColor: "#dc2626",
	},
});

const loadingStyle = css({
	textAlign: "center",
	padding: "40px",
	color: "#6b7280",
	fontSize: "14px",
});

interface ApiKeyInfo {
	username: string;
	email: string;
	expires_at: string;
	created_at: string;
}

interface KeyInfoPageProps {
	onActivateNewKey: () => void;
	onLogout: () => void;
	onBack?: () => void;
}

const KeyInfoPage = ({ onActivateNewKey, onLogout, onBack }: KeyInfoPageProps) => {
	const [keyInfo, setKeyInfo] = useState<ApiKeyInfo | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	const fetchKeyInfo = async () => {
		try {
			const info = await invoke<ApiKeyInfo | null>("get_key_info");
			setKeyInfo(info);
		} catch (error) {
			console.error("Failed to fetch key info:", error);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchKeyInfo();
	}, []);

	const formatDate = (dateString: string) => {
		try {
			// Handle the case where dateString might be undefined or null
			if (!dateString || dateString === "null" || dateString === "undefined") {
				return "No date provided";
			}

			// Create date object
			const date = new Date(dateString);

			if (isNaN(date.getTime())) {
				// Try to parse ISO format manually: "2027-06-26T09:19:00.000Z"
				const isoMatch = dateString.match(/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{3}))?Z?$/);
				if (isoMatch) {
					const [, year, month, day, hour, minute, second, ms = "0"] = isoMatch;
					const manualDate = new Date(Date.UTC(
						parseInt(year),
						parseInt(month) - 1, // Month is 0-based
						parseInt(day),
						parseInt(hour),
						parseInt(minute),
						parseInt(second),
						parseInt(ms)
					));

					if (!isNaN(manualDate.getTime())) {
						return manualDate.toLocaleDateString("en-US", {
							year: "numeric",
							month: "short",
							day: "numeric",
							hour: "2-digit",
							minute: "2-digit",
							timeZone: "UTC"
						});
					}
				}

				return "Invalid date format";
			}

			// Use simple formatting
			return date.toLocaleDateString("en-US", {
				year: "numeric",
				month: "short",
				day: "numeric",
				hour: "2-digit",
				minute: "2-digit",
			});
		} catch (error) {
			return "Date formatting error";
		}
	};

	const isExpired = (expiresAt: string) => {
		try {
			const expiryDate = new Date(expiresAt);
			if (isNaN(expiryDate.getTime())) {
				return false;
			}
			return expiryDate < new Date();
		} catch {
			return false;
		}
	};

	const handleActivateNewKey = async () => {
		try {
			await invoke("activate_new_key");
			onActivateNewKey();
		} catch (error) {
			console.error("Failed to activate new key:", error);
		}
	};

	const handleLogout = async () => {
		try {
			await invoke("clear_validated_key");
			onLogout();
		} catch (error) {
			console.error("Failed to logout:", error);
		}
	};

	if (isLoading) {
		return (
			<div className={pageStyle}>
				<div className={loadingStyle}>
					Loading API key information...
				</div>
			</div>
		);
	}

	if (!keyInfo) {
		return (
			<div className={pageStyle}>
				<div className={loadingStyle}>
					No API key information available
				</div>
			</div>
		);
	}

	const expired = isExpired(keyInfo.expires_at);

	return (
		<div className={pageStyle}>
			<div className={headerStyle}>
				<div className={hstack({ gap: "12px", alignItems: "center", marginBottom: "12px" })}>
					{onBack && (
						<button
							onClick={onBack}
							className={css({
								backgroundColor: "#f3f4f6",
								border: "1px solid #d1d5db",
								borderRadius: "6px",
								padding: "6px",
								cursor: "pointer",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								transition: "all 0.2s",
								"&:hover": {
									backgroundColor: "#e5e7eb",
								},
							})}
						>
							<TbArrowLeft size={16} className={css({ color: "#6b7280" })} />
						</button>
					)}
					<div className={css({
						backgroundColor: expired ? "#ef4444" : "#10b981",
						borderRadius: "8px",
						padding: "8px",
					})}>
						<TbKey size={20} className={css({ color: "white" })} />
					</div>
					<div>
						<h1 className={titleStyle}>API Key Information</h1>
						<p className={subtitleStyle}>
							Manage your API key and view account details
						</p>
					</div>
				</div>

				<div className={expired ? expiredBadgeStyle : statusBadgeStyle}>
					<TbKey size={16} />
					{expired ? "Key Expired" : "Key Active"}
				</div>
			</div>

			<div className={contentStyle}>
				<div className={infoSectionStyle}>
					<div className={infoItemStyle}>
						<div className={hstack({ gap: "6px", marginBottom: "4px" })}>
							<TbUser size={16} className={css({ color: "#6b7280" })} />
							<div className={infoLabelStyle}>Username</div>
						</div>
						<div className={infoValueStyle}>{keyInfo.username}</div>
					</div>

					<div className={infoItemStyle}>
						<div className={hstack({ gap: "6px", marginBottom: "4px" })}>
							<TbMail size={16} className={css({ color: "#6b7280" })} />
							<div className={infoLabelStyle}>Email</div>
						</div>
						<div className={infoValueStyle}>{keyInfo.email}</div>
					</div>

					<div className={infoItemStyle}>
						<div className={hstack({ gap: "6px", marginBottom: "4px" })}>
							<TbCalendar size={16} className={css({ color: "#6b7280" })} />
							<div className={infoLabelStyle}>Expires At</div>
						</div>
						<div className={css({
							fontSize: "14px",
							fontWeight: "500",
							wordBreak: "break-all",
							color: expired ? "#dc2626" : "#111827",
						})}>
							{formatDate(keyInfo.expires_at)}
						</div>
					</div>

					<div className={infoItemStyle}>
						<div className={hstack({ gap: "6px", marginBottom: "4px" })}>
							<TbClock size={16} className={css({ color: "#6b7280" })} />
							<div className={infoLabelStyle}>Created At</div>
						</div>
						<div className={infoValueStyle}>{formatDate(keyInfo.created_at)}</div>
					</div>
				</div>

				<div className={hstack({ gap: "12px", marginTop: "20px", justifyContent: "center" })}>
					<button onClick={handleActivateNewKey} className={primaryButtonStyle}>
						<TbRefresh size={16} />
						Activate New Key
					</button>
					<button onClick={handleLogout} className={dangerButtonStyle}>
						<TbLogout size={16} />
						Logout
					</button>
				</div>
			</div>
		</div>
	);
};

export default KeyInfoPage;
