import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TbX } from "react-icons/tb";
import { css } from "styled-system/css";
import { hstack, vstack } from "styled-system/patterns";
import { invoke } from "@tauri-apps/api/core";

const overlayStyle = css({
	position: "fixed",
	top: 0,
	left: 0,
	right: 0,
	bottom: 0,
	backgroundColor: "rgba(0, 0, 0, 0.5)",
	display: "flex",
	alignItems: "center",
	justifyContent: "center",
	zIndex: 1000,
});

const modalStyle = css({
	backgroundColor: "white",
	borderRadius: "12px",
	padding: "32px",
	maxWidth: "500px",
	width: "90%",
	boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
});

const headerStyle = css({
	textAlign: "center",
	marginBottom: "24px",
});

const titleStyle = css({
	fontSize: "24px",
	fontWeight: "700",
	color: "#111827",
	marginBottom: "8px",
});

const subtitleStyle = css({
	fontSize: "16px",
	color: "#6b7280",
	lineHeight: "1.5",
});

const inputGroupStyle = vstack({
	gap: "16px",
	marginBottom: "24px",
});

const labelStyle = css({
	fontSize: "14px",
	fontWeight: "600",
	color: "#374151",
	marginBottom: "8px",
});

const inputStyle = css({
	width: "100%",
	padding: "12px 16px",
	border: "2px solid #e5e7eb",
	borderRadius: "8px",
	fontSize: "16px",
	fontFamily: "monospace",
	letterSpacing: "0.5px",
	"&:focus": {
		outline: "none",
		borderColor: "#6366f1",
		boxShadow: "0 0 0 3px rgba(99, 102, 241, 0.1)",
	},
	"&::placeholder": {
		color: "#9ca3af",
		fontFamily: "inherit",
	},
});

const primaryButtonStyle = css({
	width: "100%",
	padding: "12px 24px",
	borderRadius: "8px",
	border: "none",
	fontSize: "16px",
	fontWeight: "600",
	cursor: "pointer",
	transition: "all 0.2s",
	display: "flex",
	alignItems: "center",
	justifyContent: "center",
	gap: "8px",
	backgroundColor: "#6366f1",
	color: "white",
	"&:hover": {
		backgroundColor: "#5856eb",
	},
	"&:disabled": {
		backgroundColor: "#9ca3af",
		cursor: "not-allowed",
	},
});

const successMessageStyle = css({
	padding: "12px 16px",
	borderRadius: "8px",
	fontSize: "14px",
	marginBottom: "16px",
	textAlign: "center",
	backgroundColor: "#d1fae5",
	color: "#065f46",
	border: "1px solid #a7f3d0",
});

const errorMessageStyle = css({
	padding: "12px 16px",
	borderRadius: "8px",
	fontSize: "14px",
	marginBottom: "16px",
	textAlign: "center",
	backgroundColor: "#fee2e2",
	color: "#dc2626",
	border: "1px solid #fca5a5",
});



interface KeyValidationResult {
	success: boolean;
	message: string;
	valid: boolean;
}

interface KeyValidationProps {
	onValidationSuccess: () => void;
}

const KeyValidation = ({ onValidationSuccess }: KeyValidationProps) => {
	const [apiKey, setApiKey] = useState("");
	const [isValidating, setIsValidating] = useState(false);
	const [validationMessage, setValidationMessage] = useState("");
	const [validationStatus, setValidationStatus] = useState<"success" | "error" | null>(null);

	const handleValidateKey = async () => {
		if (!apiKey.trim()) {
			setValidationMessage("Please enter an API key");
			setValidationStatus("error");
			return;
		}

		setIsValidating(true);
		setValidationMessage("");
		setValidationStatus(null);

		try {
			const result = await invoke<KeyValidationResult>("validate_api_key", {
				key: apiKey.trim()
			});

			if (result.success && result.valid) {
				setValidationMessage("API key validated successfully!");
				setValidationStatus("success");
				// Wait a moment to show success message, then close
				setTimeout(() => {
					onValidationSuccess();
				}, 1500);
			} else {
				setValidationMessage(result.message || "Invalid API key");
				setValidationStatus("error");
			}
		} catch (error) {
			console.error("Key validation error:", error);
			setValidationMessage("Failed to validate API key: " + String(error));
			setValidationStatus("error");
		} finally {
			setIsValidating(false);
		}
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && !isValidating) {
			handleValidateKey();
		}
	};



	return (
		<div className={overlayStyle}>
			<div className={modalStyle}>
				<div className={headerStyle}>
					<div className={hstack({ gap: "12px", justifyContent: "center", marginBottom: "16px" })}>
						<div className={css({
							backgroundColor: "#6366f1",
							borderRadius: "12px",
							padding: "12px",
						})}>
							<TbKey size={32} className={css({ color: "white" })} />
						</div>
					</div>
					<h2 className={titleStyle}>API Key Required</h2>
					<p className={subtitleStyle}>
						Please enter your API key to activate the application and access all image processing features.
					</p>
				</div>

				{validationMessage && (
					<div className={validationStatus === "success" ? successMessageStyle : errorMessageStyle}>
						<div className={hstack({ gap: "8px", justifyContent: "center" })}>
							{validationStatus === "success" ? (
								<TbCheck size={16} />
							) : (
								<TbX size={16} />
							)}
							{validationMessage}
						</div>
					</div>
				)}

				<div className={inputGroupStyle}>
					<div>
						<label className={labelStyle}>API Key</label>
						<input
							type="text"
							value={apiKey}
							onChange={(e) => setApiKey(e.target.value)}
							onKeyPress={handleKeyPress}
							placeholder="Enter your API key here..."
							className={inputStyle}
							disabled={isValidating}
						/>
					</div>

					<button
						onClick={handleValidateKey}
						disabled={isValidating || !apiKey.trim()}
						className={primaryButtonStyle}
					>
						{isValidating ? (
							<>
								<TbLoader2 size={20} className={css({ animation: "spin 1s linear infinite" })} />
								Validating...
							</>
						) : (
							<>
								<TbKey size={20} />
								Validate API Key
							</>
						)}
					</button>
				</div>
			</div>
		</div>
	);
};

export default KeyValidation;
