import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Tb<PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>etting<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ogout } from "react-icons/tb";
import { css, cx } from "styled-system/css";
import { hstack, vstack } from "styled-system/patterns";
import { invoke } from "@tauri-apps/api/core";

const sidebarStyle = css({
	position: "fixed",
	top: 0,
	left: 0,
	bottom: 0,
	width: 200,
	height: "100vh",
	backgroundColor: "white",
	overflow: "auto",
	borderRight: "1px solid #f1f1f1",
});

const headerStyle = css({
	padding: "16px",
	borderBottom: "1px solid #f1f1f1",
});

const brandStyle = css({
	fontSize: "16px",
	fontWeight: "600",
	color: "#6366f1",
	marginBottom: "4px",
});

const subtitleStyle = css({
	fontSize: "12px",
	color: "#6b7280",
});

const sectionStyle = css({
	padding: "16px 0",
});

const sectionTitleStyle = css({
	fontSize: "12px",
	fontWeight: "600",
	color: "#374151",
	textTransform: "uppercase",
	letterSpacing: "0.05em",
	marginBottom: "8px",
	paddingLeft: "16px",
});

const sidebarItemStyle = hstack({
	padding: "12px 16px",
	fontSize: "14px",
	cursor: "pointer",
	gap: "12px",
	"&:hover": {
		backgroundColor: "#f9fafb",
	},
});

const activeItemStyle = css({
	backgroundColor: "#f3f4f6",
	borderRight: "3px solid #6366f1",
});

// Helper function to check if we're running in Tauri context
const isTauriContext = () => {
	return typeof window !== 'undefined' && (
		(window as any).__TAURI__ !== undefined ||
		(window as any).__TAURI_INTERNALS__ !== undefined ||
		typeof invoke !== 'undefined'
	);
};

const Sidebar = () => {
	const handleClearKey = async () => {
		if (!isTauriContext()) {
			console.log("Not in Tauri context, skipping key clear");
			return;
		}

		try {
			const result = await invoke<boolean>("clear_validated_key");
			if (result) {
				// Reload the page to trigger key validation again
				window.location.reload();
			}
		} catch (error) {
			console.error("Failed to clear API key:", error);
		}
	};

	return (
		<div className={sidebarStyle}>
			<div className={headerStyle}>
				<div className={brandStyle}>PinkTool</div>
				<div className={subtitleStyle}>Professional Tools</div>
			</div>

			<div className={sectionStyle}>
				<div className={sectionTitleStyle}>Processing Tools</div>
				<div className={vstack({ gap: "0", alignItems: "stretch" })}>
					<div className={cx(sidebarItemStyle, activeItemStyle)}>
						<TbPhoto size={20} className={css({ color: "#6366f1" })} />
						Image Processing
					</div>
					<div className={sidebarItemStyle}>
						<TbStack2 size={20} className={css({ color: "#6b7280" })} />
						Batch Processing
					</div>
					<div className={sidebarItemStyle}>
						<TbSparkles size={20} className={css({ color: "#6b7280" })} />
						AI Enhancement
					</div>
					<div className={sidebarItemStyle}>
						<TbSettings size={20} className={css({ color: "#6b7280" })} />
						Settings
					</div>
				</div>
			</div>

			{isTauriContext() && (
				<div className={sectionStyle}>
					<div className={sectionTitleStyle}>Account</div>
					<div className={vstack({ gap: "0", alignItems: "stretch" })}>
						<div className={sidebarItemStyle}>
							<TbKey size={20} className={css({ color: "#10b981" })} />
							API Key Active
						</div>
						<div
							className={cx(sidebarItemStyle, css({
								"&:hover": {
									backgroundColor: "#fef2f2",
									color: "#dc2626",
								},
							}))}
							onClick={handleClearKey}
						>
							<TbLogout size={20} className={css({ color: "#ef4444" })} />
							Clear API Key
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default Sidebar;
